import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Types of AR content that can be associated with an experience
enum ARContentType {
  /// No AR content available
  none,

  /// 3D model that can be placed in the environment
  model3d,

  /// 360-degree panoramic view
  panorama360,

  /// Augmented information overlaid on real-world objects
  informationOverlay,

  /// Interactive AR game or activity
  interactive,

  /// AR navigation or guided tour
  navigation,
}

/// A cultural experience offered through the CultureConnect platform.
///
/// This model represents a cultural experience that users can browse, book, and participate in.
/// Experiences include various types of cultural activities such as cooking classes,
/// cultural tours, music and dance workshops, and more.
///
/// Each experience includes details about the activity, pricing, location, guide information,
/// availability, and other relevant metadata.
class Experience {
  /// Unique identifier for the experience
  final String id;

  /// Title or name of the experience
  final String title;

  /// Detailed description of what the experience entails
  final String description;

  /// URL to the main image representing the experience
  final String imageUrl;

  /// Average rating of the experience (0-5 scale)
  final double rating;

  /// Number of reviews submitted for this experience
  final int reviewCount;

  /// Price of the experience in local currency
  final double price;

  /// Category the experience belongs to (e.g., "Cooking Classes", "Cultural Tours")
  final String category;

  /// Text description of the experience location (e.g., "Lagos, Nigeria")
  final String location;

  /// Geographic coordinates (latitude and longitude) of the experience location
  final LatLng coordinates;

  /// Unique identifier for the guide or host of the experience
  final String guideId;

  /// Name of the guide or host of the experience
  final String guideName;

  /// URL to the profile image of the guide or host
  final String guideImageUrl;

  /// Languages in which the experience is offered
  final List<String> languages;

  /// Items or services included in the experience price
  final List<String> includedItems;

  /// Requirements or prerequisites for participants
  final List<String> requirements;

  /// Date and time when the experience was first created
  final DateTime createdAt;

  /// Date and time when the experience was last updated
  final DateTime updatedAt;

  /// Duration of the experience in hours
  final double durationHours;

  /// Whether the experience is accessible to people with disabilities
  final bool isAccessible;

  /// Dates when the experience is available for booking
  final List<DateTime> availableDates;

  /// Maximum number of participants allowed for the experience
  final int maxParticipants;

  /// Current number of participants booked for the experience
  final int currentParticipants;

  /// Whether the experience is featured on the platform
  final bool isFeatured;

  /// Number of times the experience has been viewed
  final int viewCount;

  /// Whether the current user has saved this experience
  final bool isSaved;

  /// Date and time when the current user last viewed this experience
  final DateTime? lastViewed;

  /// Whether this experience has AR content available
  final bool hasARContent;

  /// URL to the 3D model for AR viewing, if available
  final String? arModelUrl;

  /// Type of AR content available for this experience
  final ARContentType arContentType;

  /// Additional metadata for AR content
  final Map<String, dynamic>? arMetadata;

  /// Creates a new Experience instance.
  ///
  /// All parameters except those with default values are required.
  ///
  /// Example:
  /// ```dart
  /// final experience = Experience(
  ///   id: '123',
  ///   title: 'Traditional Cooking Class',
  ///   description: 'Learn authentic cuisine from local chefs',
  ///   imageUrl: 'https://example.com/image.jpg',
  ///   rating: 4.5,
  ///   reviewCount: 128,
  ///   price: 50,
  ///   category: 'Cooking Classes',
  ///   location: 'Lagos, Nigeria',
  ///   coordinates: const LatLng(6.5244, 3.3792),
  ///   guideId: 'guide1',
  ///   guideName: 'Chef Ade',
  ///   guideImageUrl: 'https://example.com/guide.jpg',
  ///   languages: ['English', 'Yoruba'],
  ///   includedItems: ['Ingredients', 'Recipe Book'],
  ///   requirements: ['Basic cooking skills'],
  ///   createdAt: DateTime.now(),
  ///   updatedAt: DateTime.now(),
  /// );
  /// ```
  Experience({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.rating,
    required this.reviewCount,
    required this.price,
    required this.category,
    required this.location,
    required this.coordinates,
    required this.guideId,
    required this.guideName,
    required this.guideImageUrl,
    required this.languages,
    required this.includedItems,
    required this.requirements,
    required this.createdAt,
    required this.updatedAt,
    this.durationHours = 2.0,
    this.isAccessible = false,
    this.availableDates = const [],
    this.maxParticipants = 10,
    this.currentParticipants = 0,
    this.isFeatured = false,
    this.viewCount = 0,
    this.isSaved = false,
    this.lastViewed,
    thisasARContent = false,
    this.arModelUrl,
    this.arContentType = ARContentType.none,
    this.arMetadata,
  });

  /// Creates an Experience instance from a JSON map.
  ///
  /// This factory constructor converts a JSON map into an Experience object,
  /// handling the parsing of various data types and providing default values
  /// where appropriate.
  ///
  /// [json] should contain all required fields for an Experience.
  /// Optional fields will use default values if not present in the JSON.
  ///
  /// Throws a [FormatException] if required fields are missing or of incorrect type.
  factory Experience.fromJson(Map<String, dynamic> json) {
    // Helper function to parse AR content type
    ARContentType parseARContentType(dynamic value) {
      if (value == null) return ARContentType.none;

      if (value is String) {
        try {
          return ARContentType.values.firstWhere(
            (type) => type.toString() == 'ARContentType.$value',
            orElse: () => ARContentType.none,
          );
        } catch (_) {
          return ARContentType.none;
        }
      }

      return ARContentType.none;
    }

    return Experience(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      imageUrl: json['imageUrl'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['reviewCount'] as int,
      price: (json['price'] as num).toDouble(),
      category: json['category'] as String,
      location: json['location'] as String,
      coordinates: json['coordinates'] != null
          ? LatLng(
              (json['coordinates']['latitude'] as num).toDouble(),
              (json['coordinates']['longitude'] as num).toDouble(),
            )
          : const LatLng(0, 0),
      guideId: json['guideId'] as String,
      guideName: json['guideName'] as String,
      guideImageUrl: json['guideImageUrl'] as String,
      languages: List<String>.from(json['languages'] as List),
      includedItems: List<String>.from(json['includedItems'] as List),
      requirements: List<String>.from(json['requirements'] as List),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      durationHours: json['durationHours'] != null
          ? (json['durationHours'] as num).toDouble()
          : 2.0,
      isAccessible: json['isAccessible'] as bool? ?? false,
      availableDates: json['availableDates'] != null
          ? List<DateTime>.from(
              (json['availableDates'] as List).map(
                (date) => DateTime.parse(date as String),
              ),
            )
          : [],
      maxParticipants: json['maxParticipants'] as int? ?? 10,
      currentParticipants: json['currentParticipants'] as int? ?? 0,
      isFeatured: json['isFeatured'] as bool? ?? false,
      viewCount: json['viewCount'] as int? ?? 0,
      isSaved: json['isSaved'] as bool? ?? false,
      lastViewed: json['lastViewed'] != null
          ? DateTime.parse(json['lastViewed'] as String)
          : null,
      hasARContent: json['hasARContent'] as bool? ?? false,
      arModelUrl: json['arModelUrl'] as String?,
      arContentType: parseARContentType(json['arContentType']),
      arMetadata: json['arMetadata'] != null
          ? Map<String, dynamic>.from(json['arMetadata'] as Map)
          : null,
    );
  }

  /// Converts this Experience instance to a JSON map.
  ///
  /// This method serializes all properties of the Experience object into a map
  /// that can be converted to JSON. DateTime objects are converted to ISO 8601 strings.
  ///
  /// Returns a [Map<String, dynamic>] containing all Experience properties.
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'imageUrl': imageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'price': price,
      'category': category,
      'location': location,
      'coordinates': {
        'latitude': coordinates.latitude,
        'longitude': coordinates.longitude,
      },
      'guideId': guideId,
      'guideName': guideName,
      'guideImageUrl': guideImageUrl,
      'languages': languages,
      'includedItems': includedItems,
      'requirements': requirements,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'durationHours': durationHours,
      'isAccessible': isAccessible,
      'availableDates':
          availableDates.map((date) => date.toIso8601String()).toList(),
      'maxParticipants': maxParticipants,
      'currentParticipants': currentParticipants,
      'isFeatured': isFeatured,
      'viewCount': viewCount,
      'isSaved': isSaved,
      'lastViewed': lastViewed?.toIso8601String(),
      'hasARContent': hasARContent,
      'arModelUrl': arModelUrl,
      'arContentType': arContentType.toString()lit('.').last,
      'arMetadata': arMetadata,
    };
  }

  /// Creates a copy of this Experience with the given fields replaced with new values.
  ///
  /// This method is useful for creating a modified version of an existing Experience
  /// without changing the original instance, following the immutable object pattern.
  ///
  /// Only the parameters that are provided will be changed in the new instance;
  /// all other properties will remain the same as in the original.
  ///
  /// Example:
  /// ```dart
  /// final updatedExperience = experience.copyWith(
  ///   rating: 4.8,
  ///   reviewCount: experienceeviewCount + 1,
  ///   isSaved: true,
  /// );
  /// ```
  ///
  /// Returns a new [Experience] instance with the updated properties.
  Experience copyWith({
    String? id,
    String? title,
    String? description,
    String? imageUrl,
    double? rating,
    int? reviewCount,
    double? price,
    String? category,
    String? location,
    LatLng? coordinates,
    String? guideId,
    String? guideName,
    String? guideImageUrl,
    List<String>? languages,
    List<String>? includedItems,
    List<String>? requirements,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? durationHours,
    bool? isAccessible,
    List<DateTime>? availableDates,
    int? maxParticipants,
    int? currentParticipants,
    bool? isFeatured,
    int? viewCount,
    bool? isSaved,
    DateTime? lastViewed,
    bool? hasARContent,
    String? arModelUrl,
    ARContentType? arContentType,
    Map<String, dynamic>? arMetadata,
  }) {
    return Experience(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      price: price ?? this.price,
      category: category ?? this.category,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      guideId: guideId ?? this.guideId,
      guideName: guideName ?? this.guideName,
      guideImageUrl: guideImageUrl ?? this.guideImageUrl,
      languages: languages ?? this.languages,
      includedItems: includedItems ?? this.includedItems,
      requirements: requirements ?? this.requirements,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      durationHours: durationHours ?? this.durationHours,
      isAccessible: isAccessible ?? this.isAccessible,
      availableDates: availableDates ?? this.availableDates,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      isFeatured: isFeatured ?? this.isFeatured,
      viewCount: viewCount ?? this.viewCount,
      isSaved: isSaved ?? this.isSaved,
      lastViewed: lastViewed ?? this.lastViewed,
      hasARContent: hasARContent ?? this.hasARContent,
      arModelUrl: arModelUrl ?? this.arModelUrl,
      arContentType: arContentType ?? this.arContentType,
      arMetadata: arMetadata ?? this.arMetadata,
    );
  }
}
