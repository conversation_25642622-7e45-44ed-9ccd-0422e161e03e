import 'package:flutter/material.dart';
import 'package:culture_connect/models/location/geo_location.dart';
import 'travel_service_base.dart';

/// Enum for car types
enum CarType {
  economy,
  compact,
  midsize,
  fullsize,
  suv,
  luxury,
  convertible,
  van,
}

/// Extension for car types
extension CarTypeExtension on CarType {
  /// Get the display name for the car type
  String get displayName {
    switch (this) {
      case CarType.economy:
        return 'Economy';
      case CarType.compact:
        return 'Compact';
      case CarType.midsize:
        return 'Midsize';
      case CarType.fullsize:
        return 'Fullsize';
      case CarType.suv:
        return 'SUV';
      case CarType.luxury:
        return 'Luxury';
      case CarType.convertible:
        return 'Convertible';
      case CarType.van:
        return 'Van';
    }
  }

  /// Get the icon for the car type
  IconData get icon {
    switch (this) {
      case CarType.economy:
        return Icons.directions_car;
      case CarType.compact:
        return Icons.directions_car;
      case CarType.midsize:
        return Icons.directions_car;
      case CarType.fullsize:
        return Icons.directions_car;
      case CarType.suv:
        return Icons.directions_car;
      case CarType.luxury:
        return Icons.directions_car;
      case CarType.convertible:
        return Icons.directions_car;
      case CarType.van:
        return Icons.airport_shuttle;
    }
  }
}

/// Enum for transmission types
enum TransmissionType {
  automatic,
  manual,
}

/// Extension for transmission types
extension TransmissionTypeExtension on TransmissionType {
  /// Get the display name for the transmission type
  String get displayName {
    switch (this) {
      case TransmissionType.automatic:
        return 'Automatic';
      case TransmissionType.manual:
        return 'Manual';
    }
  }
}

/// Enum for fuel types
enum FuelType {
  gasoline,
  diesel,
  electric,
  hybrid,
}

/// Extension for fuel types
extension FuelTypeExtension on FuelType {
  /// Get the display name for the fuel type
  String get displayName {
    switch (this) {
      case FuelType.gasoline:
        return 'Gasoline';
      case FuelType.diesel:
        return 'Diesel';
      case FuelType.electric:
        return 'Electric';
      case FuelTypeybrid:
        return 'Hybrid';
    }
  }

  /// Get the icon for the fuel type
  IconData get icon {
    switch (this) {
      case FuelType.gasoline:
        return Icons.local_gas_station;
      case FuelType.diesel:
        return Icons.local_gas_station;
      case FuelType.electric:
        return Icons.electric_car;
      case FuelTypeybrid:
        return Icons.electric_car;
    }
  }
}

/// A model representing a car rental
class CarRental extends TravelService {
  /// Make of the car
  final String make;

  /// Model of the car
  final String model;

  /// Year of the car
  final int year;

  /// Type of the car
  final CarType carType;

  /// Transmission type of the car
  final TransmissionType transmission;

  /// Fuel type of the car
  final FuelType fuelType;

  /// Number of seats in the car
  final int seats;

  /// Number of doors in the car
  final int doors;

  /// Whether the car has air conditioning
  final bool hasAirConditioning;

  /// Whether the car has GPS
  final bool hasGPS;

  /// Whether the car has Bluetooth
  final bool hasBluetooth;

  /// Whether the car has a USB port
  final bool hasUSB;

  /// Whether the car has a sunroof
  final bool hasSunroof;

  /// Luggage capacity in number of bags
  final int luggageCapacity;

  /// Mileage limit per day (null for unlimited)
  final int? mileageLimit;

  /// Pickup location
  final String pickupLocation;

  /// Pickup coordinates
  final GeoLocation pickupCoordinates;

  /// Dropoff location
  final String dropoffLocation;

  /// Dropoff coordinates
  final GeoLocation dropoffCoordinates;

  /// Rental company
  final String rentalCompany;

  /// Rental company logo URL
  final String rentalCompanyLogoUrl;

  /// Creates a new car rental
  const CarRental({
    required super.id,
    required super.name,
    required super.description,
    required super.price,
    required super.currency,
    required super.rating,
    required super.reviewCount,
    required super.imageUrl,
    required super.additionalImages,
    required super.provider,
    required super.location,
    required super.coordinates,
    required super.isAvailable,
    required super.isFeatured,
    required super.isOnSale,
    super.originalPrice,
    super.discountPercentage,
    required super.tags,
    required super.amenities,
    required super.cancellationPolicy,
    required super.createdAt,
    required super.updatedAt,
    required this.make,
    required this.model,
    required this.year,
    required this.carType,
    required this.transmission,
    required this.fuelType,
    required this.seats,
    required this.doors,
    required thisasAirConditioning,
    required thisasGPS,
    required thisasBluetooth,
    required thisasUSB,
    required thisasSunroof,
    required this.luggageCapacity,
    this.mileageLimit,
    required this.pickupLocation,
    required this.pickupCoordinates,
    required this.dropoffLocation,
    required this.dropoffCoordinates,
    required thisentalCompany,
    required thisentalCompanyLogoUrl,
  });

  @override
  IconData get icon => Icons.directions_car;

  @override
  Color get color => Colors.blue;

  /// Get the full name of the car
  String get fullName => '$year $make $model';

  /// Get the formatted mileage limit
  String get formattedMileageLimit {
    if (mileageLimit == null) return 'Unlimited';
    return '$mileageLimit miles/day';
  }

  /// Creates a copy of this car rental with the given fields replaced
  CarRental copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    double? rating,
    int? reviewCount,
    String? imageUrl,
    List<String>? additionalImages,
    String? provider,
    String? location,
    GeoLocation? coordinates,
    bool? isAvailable,
    bool? isFeatured,
    bool? isOnSale,
    double? originalPrice,
    double? discountPercentage,
    List<String>? tags,
    List<String>? amenities,
    String? cancellationPolicy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? make,
    String? model,
    int? year,
    CarType? carType,
    TransmissionType? transmission,
    FuelType? fuelType,
    int? seats,
    int? doors,
    bool? hasAirConditioning,
    bool? hasGPS,
    bool? hasBluetooth,
    bool? hasUSB,
    bool? hasSunroof,
    int? luggageCapacity,
    int? mileageLimit,
    String? pickupLocation,
    GeoLocation? pickupCoordinates,
    String? dropoffLocation,
    GeoLocation? dropoffCoordinates,
    String? rentalCompany,
    String? rentalCompanyLogoUrl,
  }) {
    return CarRental(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      rating: rating ?? thisating,
      reviewCount: reviewCount ?? thiseviewCount,
      imageUrl: imageUrl ?? this.imageUrl,
      additionalImages: additionalImages ?? this.additionalImages,
      provider: provider ?? this.provider,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      isOnSale: isOnSale ?? this.isOnSale,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      tags: tags ?? this.tags,
      amenities: amenities ?? this.amenities,
      cancellationPolicy: cancellationPolicy ?? this.cancellationPolicy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      carType: carType ?? this.carType,
      transmission: transmission ?? this.transmission,
      fuelType: fuelType ?? this.fuelType,
      seats: seats ?? this.seats,
      doors: doors ?? this.doors,
      hasAirConditioning: hasAirConditioning ?? thisasAirConditioning,
      hasGPS: hasGPS ?? thisasGPS,
      hasBluetooth: hasBluetooth ?? thisasBluetooth,
      hasUSB: hasUSB ?? thisasUSB,
      hasSunroof: hasSunroof ?? thisasSunroof,
      luggageCapacity: luggageCapacity ?? this.luggageCapacity,
      mileageLimit: mileageLimit ?? this.mileageLimit,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      pickupCoordinates: pickupCoordinates ?? this.pickupCoordinates,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      dropoffCoordinates: dropoffCoordinates ?? this.dropoffCoordinates,
      rentalCompany: rentalCompany ?? thisentalCompany,
      rentalCompanyLogoUrl: rentalCompanyLogoUrl ?? thisentalCompanyLogoUrl,
    );
  }
}
